#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C5版本信息命令解析器
根据C5版本信息协议解析文档实现版本数据的解析功能
"""

import json
import struct
from datetime import datetime
from typing import List, Dict, Any, Optional


class C5VersionParser:
    """
    C5版本信息解析器类
    用于解析C5命令返回的设备版本信息数据
    """
    
    def __init__(self):
        """初始化版本信息字段映射"""
        self.version_fields = [
            "主控CPU板DSP版本号",
            "主控CPU板CPLD版本号", 
            "主控A相PWM板DSP版本号",
            "主控A相PWM板FPGA版本号",
            "主控B相PWM板DSP版本号",
            "主控B相PWM板FPGA版本号",
            "主控C相PWM板DSP版本号",
            "主控C相PWM板FPGA版本号",
            "主控COMM板DSP版本号",
            "主控COMM板CPLD版本号",
            "主控CPU板FPGA版本号",
            "主控CPU板子版本号"
        ]
        
        # 版本字段的英文标识（用于JSON输出）
        self.version_field_ids = [
            "main_cpu_dsp_version",
            "main_cpu_cpld_version",
            "main_a_pwm_dsp_version", 
            "main_a_pwm_fpga_version",
            "main_b_pwm_dsp_version",
            "main_b_pwm_fpga_version",
            "main_c_pwm_dsp_version",
            "main_c_pwm_fpga_version",
            "main_comm_dsp_version",
            "main_comm_cpld_version",
            "main_cpu_fpga_version",
            "main_cpu_sub_version"
        ]
    
    def format_version(self, version_value: int) -> str:
        """
        格式化版本号显示
        
        Args:
            version_value: 32位版本号整数值
            
        Returns:
            格式化后的版本号字符串
        """
        if version_value == 0x88888888:
            return "无效"
        elif version_value == 0x00000000:
            return "0.0.0.0"
        else:
            # 根据版本号的特征判断格式化方式
            if version_value > 0x20000000:  # 可能是日期格式
                # 尝试解析为日期格式 YYMMDD
                byte3 = (version_value >> 24) & 0xFF
                byte2 = (version_value >> 16) & 0xFF  
                byte1 = (version_value >> 8) & 0xFF
                byte0 = version_value & 0xFF
                
                if byte3 >= 20 and byte3 <= 99 and byte2 <= 12 and byte1 <= 31:
                    return f"{byte3:02d}-{byte2:02d}-{byte1:02d}"
            
            # 默认按点分十进制格式显示
            byte3 = (version_value >> 24) & 0xFF
            byte2 = (version_value >> 16) & 0xFF
            byte1 = (version_value >> 8) & 0xFF  
            byte0 = version_value & 0xFF
            return f"{byte3}.{byte2}.{byte1}.{byte0}"
    
    def parse_version_bytes(self, data_bytes: bytes) -> Dict[str, Any]:
        """
        解析48字节版本数据
        
        Args:
            data_bytes: 48字节版本数据
            
        Returns:
            解析后的版本信息字典
        """
        if len(data_bytes) != 48:
            raise ValueError(f"版本数据长度错误，期望48字节，实际{len(data_bytes)}字节")
        
        versions = {}
        
        for i in range(12):
            start_idx = i * 4
            version_bytes = data_bytes[start_idx:start_idx + 4]
            
            # 小端字节序转换为整数
            version_value = struct.unpack('<I', version_bytes)[0]
            
            field_name = self.version_fields[i]
            field_id = self.version_field_ids[i]
            
            versions[field_id] = {
                "name": field_name,
                "raw_value": f"0x{version_value:08X}",
                "raw_bytes": version_bytes.hex().upper(),
                "formatted": self.format_version(version_value),
                "is_valid": version_value != 0x88888888
            }
        
        return versions
    
    def parse_c5_response(self, raw_data: bytes) -> Dict[str, Any]:
        """
        解析完整的C5响应帧
        
        Args:
            raw_data: 完整的C5响应帧数据
            
        Returns:
            解析结果字典
        """
        result = {
            "command": "C5",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "raw_hex": raw_data.hex().upper(),
            "frame_length": len(raw_data),
            "versions": {}
        }
        
        try:
            # 基本长度检查
            if len(raw_data) < 60:
                result["error"] = f"响应帧长度不足，期望60字节，实际{len(raw_data)}字节"
                return result
            
            # 帧头检查
            if raw_data[:4] != b'\xEB\x90\x01\x01':
                result["error"] = "帧头错误，期望EB900101"
                return result
            
            # 命令码检查
            if raw_data[4] != 0xC5:
                result["error"] = f"命令码错误，期望C5，实际{raw_data[4]:02X}"
                return result
            
            # 数据长度检查
            data_length = raw_data[5]
            if data_length != 0x30:
                result["error"] = f"数据长度错误，期望0x30(48)，实际0x{data_length:02X}({data_length})"
                return result
            
            # 帧尾检查
            if raw_data[-2:] != b'\xAA\xAB':
                result["error"] = "帧尾错误，期望AAAB"
                return result
            
            # 提取版本数据（跳过帧头4字节+命令1字节+长度1字节+保留3字节=9字节）
            version_data = raw_data[9:57]  # 48字节版本数据
            
            # 解析版本信息
            result["versions"] = self.parse_version_bytes(version_data)
            result["success"] = True
            
            # 统计有效版本数量
            valid_count = sum(1 for v in result["versions"].values() if v["is_valid"])
            result["valid_version_count"] = valid_count
            result["total_version_count"] = 12
            
        except Exception as e:
            result["error"] = f"解析异常: {str(e)}"
        
        return result
    
    def parse_c5_hex_string(self, hex_string: str) -> Dict[str, Any]:
        """
        解析十六进制字符串格式的C5响应数据
        
        Args:
            hex_string: 十六进制字符串，如"EB900101C5..."
            
        Returns:
            解析结果字典
        """
        try:
            # 移除空格和其他分隔符
            hex_clean = hex_string.replace(' ', '').replace('-', '').replace(':', '')
            
            # 转换为字节数据
            raw_data = bytes.fromhex(hex_clean)
            
            return self.parse_c5_response(raw_data)
            
        except ValueError as e:
            return {
                "command": "C5",
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": f"十六进制字符串格式错误: {str(e)}",
                "raw_hex": hex_string,
                "versions": {}
            }
    
    def format_output(self, parse_result: Dict[str, Any], format_type: str = "json") -> str:
        """
        格式化输出解析结果
        
        Args:
            parse_result: 解析结果字典
            format_type: 输出格式，支持"json"、"table"、"summary"
            
        Returns:
            格式化后的字符串
        """
        if format_type == "json":
            return json.dumps(parse_result, ensure_ascii=False, indent=2)
        
        elif format_type == "table":
            output = []
            output.append("=" * 80)
            output.append("C5版本信息解析结果")
            output.append("=" * 80)
            output.append(f"解析状态: {'成功' if parse_result['success'] else '失败'}")
            output.append(f"时间戳: {parse_result['timestamp']}")
            
            if parse_result.get('error'):
                output.append(f"错误信息: {parse_result['error']}")
            
            if parse_result['success']:
                output.append(f"有效版本: {parse_result.get('valid_version_count', 0)}/{parse_result.get('total_version_count', 12)}")
                output.append("")
                output.append("版本信息详情:")
                output.append("-" * 80)
                
                for field_id, version_info in parse_result['versions'].items():
                    status = "✓" if version_info['is_valid'] else "✗"
                    output.append(f"{status} {version_info['name']:<20} | {version_info['formatted']:<15} | {version_info['raw_value']}")
            
            return "\n".join(output)
        
        elif format_type == "summary":
            if parse_result['success']:
                valid_versions = [v for v in parse_result['versions'].values() if v['is_valid']]
                return f"C5版本信息解析成功，共{len(valid_versions)}个有效版本"
            else:
                return f"C5版本信息解析失败: {parse_result.get('error', '未知错误')}"
        
        else:
            return json.dumps(parse_result, ensure_ascii=False, indent=2)


def main():
    """
    主函数，演示C5版本信息解析器的使用
    """
    parser = C5VersionParser()
    
    # 示例数据
    example_hex = "EB900101C53000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101AAAB"
    
    print("C5版本信息解析器演示")
    print("=" * 60)
    print(f"示例数据: {example_hex}")
    print()
    
    # 解析示例数据
    result = parser.parse_c5_hex_string(example_hex)
    
    # 输出不同格式的结果
    print("表格格式输出:")
    print(parser.format_output(result, "table"))
    print()
    
    print("JSON格式输出:")
    print(parser.format_output(result, "json"))


if __name__ == "__main__":
    main()
