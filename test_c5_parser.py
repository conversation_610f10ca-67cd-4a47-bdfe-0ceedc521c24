#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C5版本信息解析器测试脚本
测试C5版本信息解析器的各种功能和边界情况
"""

import json
from c5_version_parser import C5VersionParser


def test_example_data():
    """测试提供的示例数据"""
    print("=" * 80)
    print("测试1: 解析提供的示例数据")
    print("=" * 80)
    
    parser = C5VersionParser()
    
    # 用户提供的示例数据
    example_data = "EB900101C53000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101AAAB"
    
    print(f"输入数据: {example_data}")
    print()
    
    result = parser.parse_c5_hex_string(example_data)
    
    # 输出表格格式结果
    print(parser.format_output(result, "table"))
    print()
    
    # 验证关键解析结果
    if result['success']:
        versions = result['versions']
        print("关键版本信息验证:")
        print(f"- 主控CPU板DSP版本: {versions['main_cpu_dsp_version']['formatted']}")
        print(f"- 主控CPU板CPLD版本: {versions['main_cpu_cpld_version']['formatted']}")
        print(f"- 有效版本数量: {result['valid_version_count']}")
    
    return result


def test_realtime_data():
    """测试从realtime_data.json中读取的实际数据"""
    print("=" * 80)
    print("测试2: 解析realtime_data.json中的实际数据")
    print("=" * 80)
    
    parser = C5VersionParser()
    
    try:
        # 读取实时数据文件
        with open('data/realtime_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 查找C5命令数据
        c5_data = None
        for item in data:
            if item.get('command') == 'C5' and item.get('success'):
                c5_data = item
                break
        
        if c5_data:
            print(f"找到C5数据，时间戳: {c5_data['timestamp']}")
            print(f"原始数据: {c5_data['raw_hex']}")
            print()
            
            # 解析数据
            result = parser.parse_c5_hex_string(c5_data['raw_hex'])
            
            # 输出结果
            print(parser.format_output(result, "table"))
            
            return result
        else:
            print("在realtime_data.json中未找到有效的C5命令数据")
            return None
            
    except FileNotFoundError:
        print("未找到data/realtime_data.json文件")
        return None
    except Exception as e:
        print(f"读取数据文件时出错: {e}")
        return None


def test_error_cases():
    """测试错误情况和边界条件"""
    print("=" * 80)
    print("测试3: 错误情况和边界条件测试")
    print("=" * 80)
    
    parser = C5VersionParser()
    
    test_cases = [
        {
            "name": "帧长度不足",
            "data": "EB900101C5300000",
            "expected_error": "响应帧长度不足"
        },
        {
            "name": "帧头错误",
            "data": "FF900101C53000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101AAAB",
            "expected_error": "帧头错误"
        },
        {
            "name": "命令码错误", 
            "data": "EB900101C43000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101AAAB",
            "expected_error": "命令码错误"
        },
        {
            "name": "数据长度错误",
            "data": "EB900101C52000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101AAAB",
            "expected_error": "数据长度错误"
        },
        {
            "name": "帧尾错误",
            "data": "EB900101C53000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101FFFF",
            "expected_error": "帧尾错误"
        },
        {
            "name": "无效十六进制字符串",
            "data": "INVALID_HEX_STRING",
            "expected_error": "十六进制字符串格式错误"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        result = parser.parse_c5_hex_string(test_case['data'])
        
        if result['success']:
            print(f"  ❌ 预期失败但解析成功")
        else:
            if test_case['expected_error'] in result.get('error', ''):
                print(f"  ✅ 正确检测到错误: {result['error']}")
            else:
                print(f"  ⚠️  错误信息不匹配")
                print(f"     期望包含: {test_case['expected_error']}")
                print(f"     实际错误: {result.get('error', 'None')}")
        print()


def test_version_formatting():
    """测试版本号格式化功能"""
    print("=" * 80)
    print("测试4: 版本号格式化功能测试")
    print("=" * 80)
    
    parser = C5VersionParser()
    
    test_versions = [
        (0x88888888, "无效"),
        (0x00000000, "0.0.0.0"),
        (0x20250306, "32.37.3.6"),  # 可能的日期格式
        (0x83150923, "131.21.9.35"),  # 版本号格式
        (0x01020304, "1.2.3.4"),
        (0xFFFFFFFF, "255.255.255.255")
    ]
    
    print("版本号格式化测试:")
    print("原始值        | 格式化结果")
    print("-" * 30)
    
    for version_value, expected in test_versions:
        formatted = parser.format_version(version_value)
        status = "✅" if formatted == expected else "❌"
        print(f"0x{version_value:08X}    | {formatted:<15} {status}")
    
    print()


def test_json_output():
    """测试JSON输出格式"""
    print("=" * 80)
    print("测试5: JSON输出格式测试")
    print("=" * 80)
    
    parser = C5VersionParser()
    
    # 使用示例数据
    example_data = "EB900101C53000000023091583060325208888888804012520888888880401252088888888040125208888888888888888000000000100000101AAAB"
    result = parser.parse_c5_hex_string(example_data)
    
    # 输出JSON格式
    json_output = parser.format_output(result, "json")
    
    try:
        # 验证JSON格式是否有效
        parsed_json = json.loads(json_output)
        print("✅ JSON格式验证通过")
        print()
        print("JSON输出示例（前200字符）:")
        print(json_output[:200] + "..." if len(json_output) > 200 else json_output)
        
        # 验证关键字段
        required_fields = ['command', 'timestamp', 'success', 'versions']
        missing_fields = [field for field in required_fields if field not in parsed_json]
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
        else:
            print("✅ 所有必需字段都存在")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式验证失败: {e}")
    
    print()


def main():
    """运行所有测试"""
    print("C5版本信息解析器测试套件")
    print("=" * 80)
    print()
    
    # 运行各项测试
    test_example_data()
    print("\n")
    
    test_realtime_data()
    print("\n")
    
    test_error_cases()
    print("\n")
    
    test_version_formatting()
    print("\n")
    
    test_json_output()
    
    print("=" * 80)
    print("所有测试完成")
    print("=" * 80)


if __name__ == "__main__":
    main()
