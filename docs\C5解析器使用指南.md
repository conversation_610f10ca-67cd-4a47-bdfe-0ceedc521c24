# C5版本信息解析器使用指南

## 概述

C5版本信息解析器是一个专门用于解析串口通信中C5版本信息命令响应数据的Python工具。它能够准确解析设备各个硬件模块的版本信息，并提供多种输出格式。

## 文件结构

```
├── c5_version_parser.py          # 主解析器模块
├── test_c5_parser.py             # 测试脚本
├── docs/
│   ├── C5版本信息协议解析文档.md    # 详细协议文档
│   └── C5解析器使用指南.md         # 本使用指南
```

## 快速开始

### 1. 基本使用

```python
from c5_version_parser import C5VersionParser

# 创建解析器实例
parser = C5VersionParser()

# 解析十六进制字符串数据
hex_data = "EB900101C53000000023091583060325208888888804012520..."
result = parser.parse_c5_hex_string(hex_data)

# 输出解析结果
print(parser.format_output(result, "table"))
```

### 2. 解析字节数据

```python
# 如果你有字节数据
raw_bytes = bytes.fromhex("EB900101C5300000002309158306032520...")
result = parser.parse_c5_response(raw_bytes)
```

### 3. 不同输出格式

```python
# 表格格式（适合控制台显示）
table_output = parser.format_output(result, "table")

# JSON格式（适合程序处理）
json_output = parser.format_output(result, "json")

# 摘要格式（简洁信息）
summary_output = parser.format_output(result, "summary")
```

## 功能特性

### 1. 完整的协议验证

- ✅ 帧头验证 (EB 90 01 01)
- ✅ 命令码验证 (C5)
- ✅ 数据长度验证 (0x30 = 48字节)
- ✅ 帧尾验证 (AA AB)
- ✅ 帧长度验证 (60字节)

### 2. 版本信息解析

支持解析12种版本信息：

| 序号 | 版本信息 | 字段ID |
|------|----------|--------|
| 1 | 主控CPU板DSP版本号 | main_cpu_dsp_version |
| 2 | 主控CPU板CPLD版本号 | main_cpu_cpld_version |
| 3 | 主控A相PWM板DSP版本号 | main_a_pwm_dsp_version |
| 4 | 主控A相PWM板FPGA版本号 | main_a_pwm_fpga_version |
| 5 | 主控B相PWM板DSP版本号 | main_b_pwm_dsp_version |
| 6 | 主控B相PWM板FPGA版本号 | main_b_pwm_fpga_version |
| 7 | 主控C相PWM板DSP版本号 | main_c_pwm_dsp_version |
| 8 | 主控C相PWM板FPGA版本号 | main_c_pwm_fpga_version |
| 9 | 主控COMM板DSP版本号 | main_comm_dsp_version |
| 10 | 主控COMM板CPLD版本号 | main_comm_cpld_version |
| 11 | 主控CPU板FPGA版本号 | main_cpu_fpga_version |
| 12 | 主控CPU板子版本号 | main_cpu_sub_version |

### 3. 智能版本格式化

- **无效版本**: 0x88888888 → "无效"
- **零版本**: 0x00000000 → "0.0.0.0"
- **标准版本**: 0x01020304 → "1.2.3.4"
- **日期格式**: 0x20250306 → "32.37.3.6"

### 4. 多种输出格式

#### 表格格式示例
```
================================================================================
C5版本信息解析结果
================================================================================
解析状态: 成功
时间戳: 2025-08-25T10:15:54.608964
有效版本: 7/12

版本信息详情:
--------------------------------------------------------------------------------
✓ 主控CPU板DSP版本号         | 131.21.9.35     | 0x83150923
✓ 主控CPU板CPLD版本号        | 32.37.3.6       | 0x20250306
✗ 主控A相PWM板DSP版本号       | 无效              | 0x88888888
```

#### JSON格式示例
```json
{
  "command": "C5",
  "timestamp": "2025-08-25T10:15:54.608964",
  "success": true,
  "error": null,
  "versions": {
    "main_cpu_dsp_version": {
      "name": "主控CPU板DSP版本号",
      "raw_value": "0x83150923",
      "raw_bytes": "23091583",
      "formatted": "131.21.9.35",
      "is_valid": true
    }
  },
  "valid_version_count": 7,
  "total_version_count": 12
}
```

## API参考

### C5VersionParser类

#### 主要方法

##### `parse_c5_hex_string(hex_string: str) -> Dict[str, Any]`
解析十六进制字符串格式的C5响应数据。

**参数:**
- `hex_string`: 十六进制字符串，支持带空格或不带空格

**返回:**
- 解析结果字典

##### `parse_c5_response(raw_data: bytes) -> Dict[str, Any]`
解析字节格式的C5响应帧。

**参数:**
- `raw_data`: 完整的C5响应帧字节数据

**返回:**
- 解析结果字典

##### `format_output(parse_result: Dict, format_type: str) -> str`
格式化输出解析结果。

**参数:**
- `parse_result`: 解析结果字典
- `format_type`: 输出格式，支持 "json"、"table"、"summary"

**返回:**
- 格式化后的字符串

#### 辅助方法

##### `parse_version_bytes(data_bytes: bytes) -> Dict[str, Any]`
解析48字节版本数据。

##### `format_version(version_value: int) -> str`
格式化单个版本号。

## 错误处理

解析器提供完善的错误处理机制：

### 常见错误类型

1. **帧长度错误**
   ```
   响应帧长度不足，期望60字节，实际XX字节
   ```

2. **帧头错误**
   ```
   帧头错误，期望EB900101
   ```

3. **命令码错误**
   ```
   命令码错误，期望C5，实际XX
   ```

4. **数据长度错误**
   ```
   数据长度错误，期望0x30(48)，实际0xXX(XX)
   ```

5. **帧尾错误**
   ```
   帧尾错误，期望AAAB
   ```

6. **十六进制格式错误**
   ```
   十六进制字符串格式错误: [具体错误信息]
   ```

### 错误处理示例

```python
result = parser.parse_c5_hex_string(invalid_data)

if not result['success']:
    print(f"解析失败: {result['error']}")
    # 处理错误情况
else:
    # 处理成功情况
    versions = result['versions']
```

## 测试

### 运行测试套件

```bash
python test_c5_parser.py
```

测试套件包含：
- ✅ 示例数据解析测试
- ✅ 实际数据解析测试  
- ✅ 错误情况测试
- ✅ 版本格式化测试
- ✅ JSON输出格式测试

### 运行演示

```bash
python c5_version_parser.py
```

## 集成示例

### 与串口通信集成

```python
import serial
from c5_version_parser import C5VersionParser

def read_c5_version(port, baudrate=19200):
    """从串口读取C5版本信息"""
    parser = C5VersionParser()
    
    try:
        with serial.Serial(port, baudrate, timeout=2) as ser:
            # 发送C5查询命令
            query_cmd = bytes.fromhex("EB900101C5000000000000000042AAAB")
            ser.write(query_cmd)
            
            # 读取响应
            response = ser.read(60)  # C5响应固定60字节
            
            # 解析响应
            result = parser.parse_c5_response(response)
            
            return result
            
    except Exception as e:
        return {"success": False, "error": str(e)}
```

### 与数据库集成

```python
import sqlite3
import json
from datetime import datetime

def save_version_to_db(parse_result, db_path="versions.db"):
    """将版本信息保存到数据库"""
    if not parse_result['success']:
        return False
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS version_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            raw_hex TEXT,
            versions_json TEXT,
            valid_count INTEGER
        )
    ''')
    
    # 插入数据
    cursor.execute('''
        INSERT INTO version_history 
        (timestamp, raw_hex, versions_json, valid_count)
        VALUES (?, ?, ?, ?)
    ''', (
        parse_result['timestamp'],
        parse_result['raw_hex'],
        json.dumps(parse_result['versions'], ensure_ascii=False),
        parse_result['valid_version_count']
    ))
    
    conn.commit()
    conn.close()
    return True
```

## 注意事项

1. **字节序**: 版本数据采用小端字节序，解析器已自动处理
2. **版本格式**: 不同模块的版本号可能采用不同的编码格式
3. **无效标识**: 0x88888888表示该模块版本无效或不存在
4. **数据完整性**: 建议在使用前验证数据的完整性和校验位

## 故障排除

### 常见问题

**Q: 解析结果显示所有版本都是"无效"**
A: 检查输入数据是否正确，特别是字节序和数据格式

**Q: 版本号格式不正确**
A: 可能需要根据具体设备调整`format_version`方法的格式化逻辑

**Q: 校验位验证失败**
A: 确认输入数据的完整性，检查是否有数据传输错误

### 调试技巧

1. 使用`raw_hex`字段检查原始数据
2. 查看`raw_bytes`字段确认字节序转换
3. 启用详细错误信息进行问题定位

## 更新日志

- **v1.0.0**: 初始版本，支持基本的C5版本信息解析
- 支持多种输出格式
- 完整的错误处理机制
- 全面的测试套件

## 许可证

本项目遵循项目整体许可证。
